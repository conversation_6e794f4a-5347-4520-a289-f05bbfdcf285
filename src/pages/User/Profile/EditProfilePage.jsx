import React, { useState, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { showToast, GlobalContext } from "Context/Global";
import { Country, State } from "country-state-city";



const EditProfilePage = () => {
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);
  const [industries, setIndustries] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    photo: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    industry_id: "",
    interested_industries: [],
    state: "",
    zip: "",
    country: ""
  });

  // Get all countries
  const countries = Country.getAllCountries();

  // Get states for selected country
  const getStatesForCountry = (countryCode) => {
    return State.getStatesOfCountry(countryCode);
  };

  useEffect(() => {
    loadProfileData();
    fetchIndustries();
  }, []);

  const fetchIndustries = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI("/v1/api/dealmaker/industries", {}, "GET");

      if (!response.error && response.data) {
        setIndustries(response.data);
      } else {
        console.error('Error fetching industries:', response.message);
        showToast(globalDispatch, "Failed to load industries", 5000, "error");
      }
    } catch (err) {
      console.error('Error in fetchIndustries:', err);
      showToast(globalDispatch, "Failed to load industries", 5000, "error");
    }
  };

  const loadProfileData = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI('/v1/api/dealmaker/user/details', {}, 'GET');

      if (!response.error) {
        // Transform the nested value structure to flat structure
        const flatData = {};
        Object.keys(response.model).forEach(key => {
          if (key === 'interested_industries') {
            // Handle interested_industries specially - convert from API format to array
            flatData[key] = response.model[key]?.value || [];

            // Ensure it's an array of numbers (IDs)
            if (Array.isArray(flatData[key])) {
              flatData[key] = flatData[key].map(id =>
                typeof id === 'string' ? parseInt(id, 10) : id
              );
            } else {
              flatData[key] = [];
            }
          } else {
            flatData[key] = response.model[key]?.value || "";
          }
        });

        // If country exists in profile data, find the matching country object
        if (flatData.country) {
          const countryObj = countries.find(c =>
            c.name.toLowerCase() === flatData.country.toLowerCase()
          );

          if (countryObj) {
            // Set the country name properly
            flatData.country = countryObj.name;
            // Set the selected country code
            setSelectedCountry(countryObj.isoCode);
          }
        }

        setFormData(flatData);
      } else {
        showToast(globalDispatch, "Failed to fetch profile details", 5000, "error");
      }
    } catch (err) {
      console.error('Error fetching profile:', err);
      showToast(globalDispatch, "Failed to fetch profile details", 5000, "error");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Handle country selection
    if (name === 'country') {
      // Find the country code from the selected country
      const countryObj = countries.find(c => c.name === value);
      if (countryObj) {
        setSelectedCountry(countryObj.isoCode);
      } else {
        setSelectedCountry("");
      }

      // Reset state when country changes
      setFormData(prev => ({
        ...prev,
        state: ""
      }));
    }
  };

  // Handle interested industries selection
  const handleInterestedIndustryChange = (industryId) => {
    setFormData(prev => {
      // Ensure interested_industries is an array
      const currentIndustries = Array.isArray(prev.interested_industries)
        ? [...prev.interested_industries]
        : [];

      // If industry is already selected, remove it
      if (currentIndustries.includes(industryId)) {
        return {
          ...prev,
          interested_industries: currentIndustries.filter(id => id !== industryId)
        };
      }
      // Otherwise add it
      else {
        return {
          ...prev,
          interested_industries: [...currentIndustries, industryId]
        };
      }
    });
  };

  const handlePhotoChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      setUploadingPhoto(true);
      const sdk = new MkdSDK();
      const formData = new FormData();
      formData.append("file", file);

      const result = await sdk.uploadImage(formData);
      if (result.url) {
        setFormData(prev => ({
          ...prev,
          photo: result.url
        }));
        showToast(globalDispatch, "Photo uploaded successfully", 5000, "success");
      }
    } catch (err) {
      console.error("Error uploading photo:", err);
      showToast(globalDispatch, "Failed to upload photo", 5000, "error");
    } finally {
      setUploadingPhoto(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);

    try {
      // Create a copy of the form data to prepare for API submission
      const apiFormData = { ...formData };

      // Format interested_industries as an object with value property for API
      if (apiFormData.interested_industries && Array.isArray(apiFormData.interested_industries)) {
        apiFormData.interested_industries = {
          value: apiFormData.interested_industries
        };
      }

      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        '/v1/api/dealmaker/user/account/update',
        apiFormData,
        'POST'
      );

      if (!response.error) {
        showToast(globalDispatch, "Profile updated successfully", 5000, "success");
        navigate('/member/profile');
      } else {
        showToast(globalDispatch, "Failed to update profile", 5000, "error");
      }
    } catch (err) {
      console.error('Error updating profile:', err);
      showToast(globalDispatch, "Failed to update profile", 5000, "error");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-[#1e1e1e]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#2e7d32]"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#1e1e1e] p-4 md:p-6">
      <div className="mb-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-xl font-semibold text-[#eaeaea]">Edit Profile</h1>
          <p className="text-sm text-[#b5b5b5]">Update your profile information</p>
        </div>
        <button
          onClick={() => navigate('/member/profile')}
          className="rounded-lg border border-[#363636] px-4 py-2 text-[#eaeaea] hover:bg-[#363636] transition-colors"
        >
          Cancel
        </button>
      </div>

      <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
        <div className="space-y-6">
          {/* Photo Upload */}
          <div className="rounded-lg border border-[#363636] bg-[#252525] p-6">
            <h3 className="mb-4 text-lg font-semibold text-[#eaeaea]">Profile Photo</h3>
            <div className="flex items-center gap-4">
              <div className="relative h-20 w-20">
                <div className="h-full w-full rounded-full bg-[#2e7d32] flex items-center justify-center overflow-hidden">
                  {formData.photo ? (
                    <img
                      src={formData.photo}
                      alt="Profile"
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <div className="text-2xl font-bold text-white">
                      {formData.first_name.charAt(0)}
                    </div>
                  )}
                </div>
                <div className="absolute bottom-0 right-0">
                  <input
                    type="file"
                    onChange={handlePhotoChange}
                    className="hidden"
                    id="photo-input"
                    accept="image/*"
                    disabled={uploadingPhoto}
                  />
                  <button
                    type="button"
                    onClick={() => document.getElementById('photo-input').click()}
                    className={`rounded-full bg-[#2e7d32] p-2 text-white hover:bg-[#1b5e20] ${
                      uploadingPhoto ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                    disabled={uploadingPhoto}
                  >
                    {uploadingPhoto ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-2 border-t-transparent"></div>
                    ) : (
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>
              <div className="flex-1">
                <p className="text-sm text-[#b5b5b5]">
                  Upload a new profile photo. Recommended size: 400x400px
                </p>
                {uploadingPhoto && (
                  <p className="text-sm text-[#2e7d32] mt-1">Uploading photo...</p>
                )}
              </div>
            </div>
          </div>

          {/* Personal Information */}
          <div className="rounded-lg border border-[#363636] bg-[#252525] p-6">
            <h3 className="mb-4 text-lg font-semibold text-[#eaeaea]">Personal Information</h3>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-[#b5b5b5] mb-2">First Name</label>
                <input
                  type="text"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleInputChange}
                  className="w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#b5b5b5] mb-2">Last Name</label>
                <input
                  type="text"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleInputChange}
                  className="w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#b5b5b5] mb-2">Email</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#b5b5b5] mb-2">Phone</label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none"
                />
              </div>
            </div>
          </div>

          {/* Address Information */}
          <div className="rounded-lg border border-[#363636] bg-[#252525] p-6">
            <h3 className="mb-4 text-lg font-semibold text-[#eaeaea]">Address Information</h3>
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label className="block text-sm font-medium text-[#b5b5b5] mb-2">Address</label>
                <input
                  type="text"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  className="w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none"
                />
              </div>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-[#b5b5b5] mb-2">City</label>
                  <input
                    type="text"
                    name="city"
                    value={formData.city}
                    onChange={handleInputChange}
                    className="w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-[#b5b5b5] mb-2">Country</label>
                  <select
                    name="country"
                    value={formData.country}
                    onChange={handleInputChange}
                    className="w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none appearance-none bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat"
                  >
                    <option value="">Select country</option>
                    {countries.map((country) => (
                      <option key={country.isoCode} value={country.name}>
                        {`+${country.phonecode || ''}`} {country.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-[#b5b5b5] mb-2">State/Province/Region</label>
                  <select
                    name="state"
                    value={formData.state}
                    onChange={handleInputChange}
                    className="w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none appearance-none bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat"
                    disabled={!selectedCountry}
                  >
                    <option value="">Select state/province/region</option>
                    {getStatesForCountry(selectedCountry).map((state) => (
                      <option key={state.isoCode} value={state.name}>
                        {state.name}
                      </option>
                    ))}
                  </select>
                  {!selectedCountry && (
                    <p className="mt-1 text-xs text-[#666]">Please select a country first</p>
                  )}
                  {selectedCountry && getStatesForCountry(selectedCountry).length === 0 && (
                    <p className="mt-1 text-xs text-[#666]">No states/provinces available for this country</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-[#b5b5b5] mb-2">ZIP Code</label>
                  <input
                    type="text"
                    name="zip"
                    value={formData.zip}
                    onChange={handleInputChange}
                    className="w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Professional Information */}
          <div className="rounded-lg border border-[#363636] bg-[#252525] p-6">
            <h3 className="mb-4 text-lg font-semibold text-[#eaeaea]">Professional Information</h3>
            <div className="mb-6">
              <label className="block text-sm font-medium text-[#b5b5b5] mb-2">Industry</label>
              <select
                name="industry_id"
                value={formData.industry_id}
                onChange={handleInputChange}
                className="w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none appearance-none bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat"
              >
                <option value="">Select industry</option>
                {industries.map((industry) => (
                  <option
                    key={industry.id?.value || industry.id}
                    value={industry.id?.value || industry.id}
                  >
                    {industry.name?.value || industry.name}
                  </option>
                ))}
                {/* Fallback options in case API doesn't return data */}
                {industries.length === 0 && (
                  <>
                    <option value="1">Technology</option>
                    <option value="2">Healthcare</option>
                    <option value="3">Finance</option>
                    <option value="4">Retail</option>
                    <option value="5">Manufacturing</option>
                    <option value="6">Education</option>
                    <option value="7">Real Estate</option>
                    <option value="8">Other</option>
                  </>
                )}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-[#b5b5b5] mb-2">Industries I'm Interested In</label>
              <p className="text-xs text-[#b5b5b5] mb-3">Select all industries you're interested in working with</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-60 overflow-y-auto p-2 border border-[#363636] rounded-lg bg-[#1e1e1e]">
                {INDUSTRY_OPTIONS.map((industry) => (
                  <div key={industry.id} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`industry-${industry.id}`}
                      checked={formData.interested_industries && Array.isArray(formData.interested_industries) ?
                        formData.interested_industries.includes(industry.id) : false}
                      onChange={() => handleInterestedIndustryChange(industry.id)}
                      className="mr-2 h-4 w-4 rounded border-[#363636] bg-[#1e1e1e] text-[#2e7d32] focus:ring-[#2e7d32]"
                    />
                    <label htmlFor={`industry-${industry.id}`} className="text-sm text-[#eaeaea]">
                      {industry.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => navigate('/member/profile')}
              className="rounded-lg border border-[#363636] px-6 py-2 text-[#eaeaea] hover:bg-[#363636] transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving || uploadingPhoto}
              className="rounded-lg bg-[#2e7d32] px-6 py-2 text-[#eaeaea] hover:bg-[#1b5e20] transition-colors disabled:opacity-50"
            >
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default EditProfilePage;